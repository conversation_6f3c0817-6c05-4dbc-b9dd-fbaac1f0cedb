# Selenium Parallel Runner

## Overview
The Selenium Parallel Runner project is designed to execute a Selenium-based automation script in parallel across multiple instances. Each instance processes a specific range of rows from a CSV file, allowing for efficient handling of large datasets.

## Project Structure
```
selenium-parallel-runner
├── src
│   ├── automation_script_token.py  # Original Selenium script for processing prompts
│   ├── parallel_runner.py           # Manages parallel execution of the automation script
│   └── utils
│       └── __init__.py              # Utility functions and classes
├── requirements.txt                 # Project dependencies
└── README.md                        # Project documentation
```

## Installation
To set up the project, follow these steps:

1. Clone the repository:
   ```
   git clone <repository-url>
   cd selenium-parallel-runner
   ```

2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage
To run the automation script in parallel, execute the `parallel_runner.py` script. This script will manage multiple instances of the Selenium automation script, each processing a designated range of rows from the specified CSV file.

### Example Command
```
python src/parallel_runner.py
```

## Functionality
- **automation_script_token.py**: Contains the main logic for interacting with web elements, processing prompts from a CSV file, and handling errors.
- **parallel_runner.py**: Responsible for launching multiple instances of the automation script, each configured to handle a specific range of rows.
- **utils/__init__.py**: A placeholder for utility functions that can be used throughout the project, such as CSV management and logging.

## Contributing
Contributions are welcome! Please submit a pull request or open an issue for any enhancements or bug fixes.

## License
This project is licensed under the MIT License. See the LICENSE file for more details.