from multiprocessing import Process
import pandas as pd
import os
import sys

def run_automation_script(start_row, end_row):
    """Run the automation script for a specific range of rows."""
    # Set the environment variable for the row range
    os.environ['START_ROW'] = str(start_row)
    os.environ['END_ROW'] = str(end_row)
    
    # Import the automation script
    import automation_script_token

if __name__ == "__main__":
    # Define the ranges for processing
    ranges = [
        (501, 875),
        (876, 1251),
        (1252, 1626),
        (1627, 2001),
        (2002, 2376),
        (2377, 2751),
        (2752, 3126),
        (3127, 3501),
        (3502, 3758)
    ]
    
    processes = []
    
    # Create and start a process for each range
    for start, end in ranges:
        p = Process(target=run_automation_script, args=(start, end))
        processes.append(p)
        p.start()
    
    # Wait for all processes to complete
    for p in processes:
        p.join()