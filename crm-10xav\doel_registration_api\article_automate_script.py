# code for promt testing in package

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import *
from webdriver_manager.chrome import ChromeDriverManager
import pandas as pd
import time

# === User Configuration ===
start_row = 453
start_idx = start_row - 1  # convert to zero-based index

# Load the CSV of prompts
csv_path = r"C:\Users\<USER>\Downloads\support_promts.csv"
df = pd.read_csv(csv_path)

# Print column names to verify
print("Available columns:", df.columns.tolist())

# Setup Chrome options
options = Options()
options.add_argument("--start-maximized")
options.add_experimental_option("detach", True)  # Keep browser open
options.add_experimental_option('excludeSwitches', ['enable-logging'])

# Initialize the driver
driver = webdriver.Chrome(
    service=Service(ChromeDriverManager().install()),
    options=options
)
driver_wait = WebDriverWait(driver, 30, poll_frequency=0.5)


def robust_find_and_click(by, locator, timeout=30):
    """Wait until clickable, scroll into view, then click (with JS fallback)."""
    elem = WebDriverWait(driver, timeout, 0.5).until(
        EC.element_to_be_clickable((by, locator))
    )
    driver.execute_script("arguments[0].scrollIntoView({block:'center'});", elem)
    try:
        elem.click()
    except (ElementClickInterceptedException,
            ElementNotInteractableException,
            StaleElementReferenceException):
        driver.execute_script("arguments[0].click();", elem)
    return elem


def robust_find_and_type(by, locator, text, timeout=30):
    """Wait until visible, scroll, clear, and type text (with JS fallback)."""
    elem = WebDriverWait(driver, timeout, 0.5).until(
        EC.visibility_of_element_located((by, locator))
    )
    driver.execute_script("arguments[0].scrollIntoView({block:'center'});", elem)
    try:
        elem.clear()
        elem.send_keys(text)
    except (ElementNotInteractableException, StaleElementReferenceException):
        driver.execute_script("arguments[0].value = arguments[1];", elem, text)
    return elem


def wait_for_overlay_to_disappear(css_selector=".loading-overlay", timeout=30):
    """Wait until any overlay/spinner disappears."""
    try:
        WebDriverWait(driver, timeout, 0.5).until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, css_selector))
        )
    except TimeoutException:
        pass


# === Main Script ===
try:
    # 1) Login
    driver.get("https://upgrade.xtenav.com/login")
    robust_find_and_type(By.NAME, "user-email", "<EMAIL>")
    robust_find_and_type(By.NAME, "user-password", "Evil@123")
    robust_find_and_click(By.ID, "loginSubmitButton")

    # 2) Start a new X-DRAW
    wait_for_overlay_to_disappear()
    robust_find_and_click(By.CSS_SELECTOR, ".xavia-button-main")

    # 3) Loop through prompts
    for idx in range(start_idx, len(df)):
        try:
            prompt = df.iloc[idx]['promt']  # Changed from 'PROMT' to 'promt'
            print(f"→ Processing CSV row {idx+1}: {prompt!r}")

            # Wait for the textarea to be ready
            wait_for_overlay_to_disappear()
            
            # Clear existing text and enter new prompt
            textarea = robust_find_and_type(
                By.CSS_SELECTOR, 
                ".dynamic-textarea.gen-ai-text-area__input", 
                prompt
            )
            
            # Add a small pause after typing
            time.sleep(1)
            
            # Press Enter
            textarea.send_keys(Keys.ENTER)
            
            # Wait for processing
            wait_for_overlay_to_disappear()
            
            # Click the outline button
            robust_find_and_click(By.CSS_SELECTOR, ".btn.btn-outline-primary")
            
            # Wait for the next prompt to be ready
            time.sleep(3)  # Increased wait time between prompts

            print(f"✓ Completed row {idx+1}")

        except Exception as e:
            print(f"⚠️ Row {idx+1} failed: {e!r}")
            try:
                driver.save_screenshot(f"error_row_{idx+1}.png")
            except:
                print("Could not save screenshot")
            
            # Add a longer pause after an error
            time.sleep(5)
            continue

finally:
    try:
        driver.quit()
    except:
        pass






